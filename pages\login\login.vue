<template>
	<view class="page-container">
		<!-- 内容区域 -->
		<view class="content-wrapper">
			<!-- 头部Logo区域 -->
			<view class="header-section">
				<view class="logo-container">
					<text class="logo-icon">💰</text>
					<text class="logo-text">次推科技</text>
				</view>
				<text class="welcome-text">{{ isLoginMode ? '欢迎回来' : '创建账号' }}</text>
			</view>

			<!-- 表单切换标签 -->
			<view class="tab-section">
				<view class="tab-container">
					<view 
						class="tab-item"
						:class="{ 'active': isLoginMode }"
						@click="switchToLogin"
					>
						<text class="tab-text">登录</text>
					</view>
					<view 
						class="tab-item"
						:class="{ 'active': !isLoginMode }"
						@click="switchToRegister"
					>
						<text class="tab-text">注册</text>
					</view>
				</view>
			</view>

			<!-- 表单区域 -->
			<view class="form-section">
				<!-- 登录表单 -->
				<view v-if="isLoginMode" class="form-container">
					<view class="form-item">
						<view class="input-container">
							<text class="input-icon">📱</text>
							<input 
								v-model="loginForm.phone"
								type="number"
								placeholder="请输入手机号"
								placeholder-style="color: rgba(255, 255, 255, 0.7)"
								class="form-input phone-input"
								maxlength="11"
							/>
						</view>
					</view>
					
					<view class="form-item">
						<view class="input-container">
							<text class="input-icon">🔐</text>
							<input 
								v-model="loginForm.password"
								:type="showPassword ? 'text' : 'password'"
								placeholder="请输入密码"
								class="form-input password-input"
							/>
							<view class="password-toggle" @click="togglePassword">
								<text class="toggle-icon">{{ showPassword ? '👁️' : '👁️‍🗨️' }}</text>
							</view>
						</view>
					</view>
					
					<view class="form-actions">
						<button 
							class="submit-btn login-btn"
							@click="handleLogin"
							:disabled="!canSubmitLogin"
						>
							<text class="btn-text">登录</text>
						</button>
					</view>
				</view>

				<!-- 注册表单 -->
				<view v-else class="form-container">
					<view class="form-item">
						<view class="input-container">
							<text class="input-icon">📱</text>
							<input 
								v-model="registerForm.phone"
								type="number"
								placeholder="请输入手机号"
								placeholder-style="color: rgba(255, 255, 255, 0.7)"
								class="form-input phone-input"
								maxlength="11"
							/>
						</view>
					</view>
					
					<view class="form-item">
						<view class="input-container">
							<text class="input-icon">🔐</text>
							<input 
								v-model="registerForm.password"
								:type="showPassword ? 'text' : 'password'"
								placeholder="请输入密码"
								class="form-input password-input"
							/>
							<view class="password-toggle" @click="togglePassword">
								<text class="toggle-icon">{{ showPassword ? '👁️' : '👁️‍🗨️' }}</text>
							</view>
						</view>
					</view>
					
					<view class="form-item">
						<view class="input-container">
							<text class="input-icon">🔒</text>
							<input 
								v-model="registerForm.confirmPassword"
								:type="showConfirmPassword ? 'text' : 'password'"
								placeholder="请确认密码"
								class="form-input confirm-password-input"
							/>
							<view class="password-toggle" @click="toggleConfirmPassword">
								<text class="toggle-icon">{{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}</text>
							</view>
						</view>
					</view>
					
					<view class="form-actions">
						<button 
							class="submit-btn register-btn"
							@click="handleRegister"
							:disabled="!canSubmitRegister"
						>
							<text class="btn-text">注册</text>
						</button>
					</view>
				</view>
			</view>

			<!-- 协议区域 -->
			<view class="agreement-section">
				<view class="agreement-container">
					<view class="checkbox-container" @click="toggleAgreement">
						<view class="checkbox" :class="{ 'checked': agreedToTerms }">
							<text v-if="agreedToTerms" class="check-icon">✓</text>
						</view>
						<text class="agreement-text">
							我已阅读并同意
							<text class="agreement-link" @click.stop="openUserAgreement">《用户协议》</text>
							和
							<text class="agreement-link" @click.stop="openPrivacyPolicy">《隐私政策》</text>
						</text>
					</view>
				</view>
			</view>

			<!-- 底部安全区域 -->
			<view class="safe-area-bottom"></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isLoginMode: true,
				showPassword: false,
				showConfirmPassword: false,
				agreedToTerms: false,
				loginForm: {
					phone: '',
					password: ''
				},
				registerForm: {
					phone: '',
					password: '',
					confirmPassword: ''
				}
			}
		},
		computed: {
			// 登录表单是否可提交
			canSubmitLogin() {
				return this.loginForm.phone.length === 11 && 
					   this.loginForm.password.length >= 6 && 
					   this.agreedToTerms
			},
			
			// 注册表单是否可提交
			canSubmitRegister() {
				return this.registerForm.phone.length === 11 && 
					   this.registerForm.password.length >= 6 && 
					   this.registerForm.confirmPassword === this.registerForm.password &&
					   this.agreedToTerms
			}
		},
		methods: {
			// 切换到登录
			switchToLogin() {
				this.isLoginMode = true
				this.clearForms()
			},
			
			// 切换到注册
			switchToRegister() {
				this.isLoginMode = false
				this.clearForms()
			},
			
			// 清空表单
			clearForms() {
				this.loginForm = { phone: '', password: '' }
				this.registerForm = { phone: '', password: '', confirmPassword: '' }
				this.showPassword = false
				this.showConfirmPassword = false
			},
			
			// 切换密码显示
			togglePassword() {
				this.showPassword = !this.showPassword
			},
			
			// 切换确认密码显示
			toggleConfirmPassword() {
				this.showConfirmPassword = !this.showConfirmPassword
			},
			
			// 切换协议同意状态
			toggleAgreement() {
				this.agreedToTerms = !this.agreedToTerms
			},
			
			// 处理登录
			handleLogin() {
				if (!this.canSubmitLogin) return
				
				// 验证手机号格式
				if (!this.validatePhone(this.loginForm.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}
				
				// 模拟登录请求
				uni.showLoading({
					title: '登录中...'
				})
				
				setTimeout(() => {
					uni.hideLoading()
					
					// 模拟登录成功
					const userInfo = {
						phone: this.loginForm.phone,
						nickname: '用户' + this.loginForm.phone.slice(-4),
						id: Math.random().toString().slice(2, 8),
						avatar: 'https://placehold.co/200x200'
					}
					
					// 保存用户信息
					uni.setStorageSync('userInfo', userInfo)
					uni.setStorageSync('token', 'mock_token_' + Date.now())
					
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					})
					
					// 跳转到首页
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/index/index'
						})
					}, 1500)
				}, 2000)
			},
			
			// 处理注册
			handleRegister() {
				if (!this.canSubmitRegister) return
				
				// 验证手机号格式
				if (!this.validatePhone(this.registerForm.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}
				
				// 验证密码一致性
				if (this.registerForm.password !== this.registerForm.confirmPassword) {
					uni.showToast({
						title: '两次输入的密码不一致',
						icon: 'none'
					})
					return
				}
				
				// 模拟注册请求
				uni.showLoading({
					title: '注册中...'
				})
				
				setTimeout(() => {
					uni.hideLoading()
					
					uni.showToast({
						title: '注册成功',
						icon: 'success'
					})
					
					// 注册成功后自动切换到登录
					setTimeout(() => {
						this.loginForm.phone = this.registerForm.phone
						this.switchToLogin()
					}, 1500)
				}, 2000)
			},
			
			// 验证手机号格式
			validatePhone(phone) {
				const phoneReg = /^1[3-9]\d{9}$/
				return phoneReg.test(phone)
			},
			
			// 打开用户协议
			openUserAgreement() {
				uni.showToast({
					title: '跳转到用户协议页面',
					icon: 'none'
				})
			},
			
			// 打开隐私政策
			openPrivacyPolicy() {
				uni.showToast({
					title: '跳转到隐私政策页面',
					icon: 'none'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
/* 全局placeholder样式 */
.page-container {
	width: 100%;
	height: 100vh;
	background: linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%);
}

/* 内容区域 */
.content-wrapper {
	height: 100vh;
	color: #ffffff;
	display: flex;
	flex-direction: column;
	padding: 0 40rpx;
}

/* 头部Logo区域 */
.header-section {
	padding: 120rpx 0 60rpx;
	text-align: center;
	
	.logo-container {
		margin-bottom: 32rpx;
		
		.logo-icon {
			font-size: 80rpx;
			display: block;
			margin-bottom: 16rpx;
		}
		
		.logo-text {
			font-size: 48rpx;
			font-weight: bold;
			color: #ffffff;
		}
	}
	
	.welcome-text {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.8);
	}
}

/* 表单切换标签 */
.tab-section {
	margin-bottom: 48rpx;
	
	.tab-container {
		display: flex;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 50rpx;
		padding: 8rpx;
		backdrop-filter: blur(10rpx);
		
		.tab-item {
			flex: 1;
			text-align: center;
			padding: 24rpx;
			border-radius: 42rpx;
			transition: all 0.3s ease;
			
			.tab-text {
				font-size: 32rpx;
				font-weight: 500;
				color: rgba(255, 255, 255, 0.7);
			}
			
			&.active {
				background: rgba(255, 255, 255, 0.2);
				
				.tab-text {
					color: #ffffff;
					font-weight: 600;
				}
			}
		}
	}
}

/* 表单区域 */
.form-section {
	flex: 1;
	
	.form-container {
		.form-item {
			margin-bottom: 32rpx;
			
			.input-container {
				position: relative;
				background: rgba(255, 255, 255, 0.1);
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				padding: 0 24rpx;
				backdrop-filter: blur(10rpx);
				border: 2rpx solid transparent;
				transition: all 0.3s ease;
				
				&:focus-within {
					border-color: rgba(255, 255, 255, 0.3);
					background: rgba(255, 255, 255, 0.15);
				}
				
				.input-icon {
					font-size: 32rpx;
					margin-right: 24rpx;
					opacity: 0.8;
				}
				
				.form-input {
					flex: 1;
					height: 96rpx;
					font-size: 32rpx;
					color: #ffffff;
					background: transparent;
					border: none;
					outline: none;
					
					&::placeholder {
						color: rgba(255, 255, 255, 0.7) !important;
					}
					
					&.phone-input::placeholder {
						color: rgba(255, 255, 255, 0.7) !important;
					}

					&.password-input::placeholder {
						color: rgba(255, 255, 255, 0.7) !important;
					}

					&.confirm-password-input::placeholder {
						color: rgba(255, 255, 255, 0.7) !important;
					}
				}
				
				// 确保所有input的placeholder都是正确的颜色 - 多浏览器兼容
				.input-container input[type="password"]::placeholder {
					color: rgba(255, 255, 255, 0.7) !important;
				}
				
				.input-container input[type="text"]::placeholder {
					color: rgba(255, 255, 255, 0.7) !important;
				}
				
				.password-input::placeholder,
				.confirm-password-input::placeholder {
					color: rgba(255, 255, 255, 0.7) !important;
				}
				
				// 使用更强的选择器
				.form-section input::placeholder {
					color: rgba(255, 255, 255, 0.7) !important;
				}
				
				// 最强选择器 - 覆盖所有可能的样式
				.page-container .form-section .input-container input::placeholder {
					color: rgba(255, 255, 255, 0.7) !important;
				}
				
				
				// 专门针对uni-input组件的样式 (使用dart-sass支持的语法)
				::v-deep .uni-input-input {
					color: #ffffff !important;
				}
				
				::v-deep .uni-input-placeholder {
					color: rgba(255, 255, 255, 0.7) !important;
				}
				
				.password-toggle {
					padding: 16rpx;
					
					.toggle-icon {
						font-size: 32rpx;
						opacity: 0.7;
					}
				}
			}
		}
		
		.form-actions {
			margin-top: 48rpx;
			
			.submit-btn {
				width: 100%;
				height: 96rpx;
				border-radius: 48rpx;
				border: none;
				font-size: 32rpx;
				font-weight: 600;
				transition: all 0.3s ease;
				
				&.login-btn {
					background: linear-gradient(135deg, #10b981, #059669);
					color: #ffffff;
					
					&:not(:disabled):active {
						transform: scale(0.98);
					}
				}
				
				&.register-btn {
					background: linear-gradient(135deg, #f59e0b, #d97706);
					color: #ffffff;
					
					&:not(:disabled):active {
						transform: scale(0.98);
					}
				}
				
				&:disabled {
					opacity: 0.5;
					background: rgba(255, 255, 255, 0.2);
					color: rgba(255, 255, 255, 0.5);
				}
				
				.btn-text {
					font-size: 32rpx;
					font-weight: 600;
				}
			}
		}
	}
}

/* 协议区域 */
.agreement-section {
	padding: 40rpx 0;
	
	.agreement-container {
		.checkbox-container {
			display: flex;
			align-items: flex-start;
			
			.checkbox {
				width: 32rpx;
				height: 32rpx;
				border: 2rpx solid rgba(255, 255, 255, 0.5);
				border-radius: 6rpx;
				margin-right: 16rpx;
				margin-top: 4rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				flex-shrink: 0;
				
				&.checked {
					background: #10b981;
					border-color: #10b981;
				}
				
				.check-icon {
					font-size: 20rpx;
					color: #ffffff;
					font-weight: bold;
				}
			}
			
			.agreement-text {
				font-size: 26rpx;
				color: rgba(255, 255, 255, 0.8);
				line-height: 1.5;
				flex: 1;
				
				.agreement-link {
					color: #60a5fa;
					text-decoration: underline;
				}
			}
		}
	}
}

/* 底部安全区域 */
.safe-area-bottom {
	height: 60rpx;
}
</style>

<style lang="scss">
/* 全局样式 - 确保所有placeholder颜色正确 */
.page-container .password-input::placeholder,
.page-container .confirm-password-input::placeholder {
	color: rgba(255, 255, 255, 0.7) !important;
}

.page-container .password-input::-webkit-input-placeholder,
.page-container .confirm-password-input::-webkit-input-placeholder {
	color: rgba(255, 255, 255, 0.7) !important;
}

.page-container .password-input::-moz-placeholder,
.page-container .confirm-password-input::-moz-placeholder {
	color: rgba(255, 255, 255, 0.7) !important;
}

.page-container .password-input:-ms-input-placeholder,
.page-container .confirm-password-input:-ms-input-placeholder {
	color: rgba(255, 255, 255, 0.7) !important;
}

/* 通用fallback - 使用更强的选择器 */
.page-container .input-container input::placeholder {
	color: rgba(255, 255, 255, 0.7) !important;
}

.page-container .form-section input::placeholder {
	color: rgba(255, 255, 255, 0.7) !important;
}

.page-container input[type="password"]::placeholder,
.page-container input[type="text"]::placeholder {
	color: rgba(255, 255, 255, 0.7) !important;
}
</style> 