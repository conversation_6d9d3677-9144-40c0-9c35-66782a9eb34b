<template>
	<view class="page-container">
		<!-- 页面内容区域 -->
		<view class="content-container">
			<view class="content-inner">
				<!-- 敬请期待内容区域 -->
				<view class="coming-soon-container">
					<!-- 主图标 -->
					<view class="main-icon-container">
						<view class="main-icon">
							<text class="icon-symbol">👥</text>
						</view>
						<view class="icon-glow"></view>
					</view>
					
					<!-- 主标题 -->
					<view class="main-title">
						<text class="title-text">邀请好友</text>
					</view>
					
					<!-- 副标题 -->
					<view class="sub-title">
						<text class="sub-text">敬请期待</text>
					</view>
					
					<!-- 描述文字 -->
					<view class="description">
						<text class="desc-text">邀请好友功能正在开发中</text>
						<text class="desc-text">敬请期待更多精彩内容</text>
					</view>
					
					<!-- 装饰元素 -->
					<view class="decoration-container">
						<view class="decoration-item" :style="{ animationDelay: '0s' }">
							<text class="decoration-icon">✨</text>
						</view>
						<view class="decoration-item" :style="{ animationDelay: '0.5s' }">
							<text class="decoration-icon">🎉</text>
						</view>
						<view class="decoration-item" :style="{ animationDelay: '1s' }">
							<text class="decoration-icon">🎊</text>
						</view>
						<view class="decoration-item" :style="{ animationDelay: '1.5s' }">
							<text class="decoration-icon">🌟</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			
		}
	}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	height: 100vh;
	background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

/* 内容区域 */
.content-container {
	height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	padding-bottom: 120rpx; /* 为tabbar留出空间 */
	
	.content-inner {
		width: 100%;
		padding: 32rpx;
		
		/* 敬请期待容器 */
		.coming-soon-container {
			text-align: center;
			
			/* 主图标容器 */
			.main-icon-container {
				position: relative;
				margin-bottom: 48rpx;
				
				.main-icon {
					width: 160rpx;
					height: 160rpx;
					background: linear-gradient(135deg, #fef2f2, #fee2e2);
					border-radius: 50%;
					display: inline-flex;
					align-items: center;
					justify-content: center;
					border: 6rpx solid #ffffff;
					box-shadow: 0 16rpx 48rpx rgba(220, 38, 38, 0.2);
					position: relative;
					z-index: 2;
					animation: bounce 2s infinite ease-in-out;
					
					.icon-symbol {
						font-size: 80rpx;
					}
				}
				
				.icon-glow {
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					width: 200rpx;
					height: 200rpx;
					background: radial-gradient(circle, rgba(239, 68, 68, 0.3) 0%, transparent 70%);
					border-radius: 50%;
					animation: glow 3s infinite ease-in-out alternate;
				}
			}
			
			/* 主标题 */
			.main-title {
				margin-bottom: 16rpx;
				
				.title-text {
					font-size: 48rpx;
					font-weight: bold;
					color: #111827;
					background: linear-gradient(135deg, #ef4444, #dc2626);
					background-clip: text;
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
				}
			}
			
			/* 副标题 */
			.sub-title {
				margin-bottom: 32rpx;
				
				.sub-text {
					font-size: 36rpx;
					font-weight: 600;
					color: #6b7280;
				}
			}
			
			/* 描述文字 */
			.description {
				margin-bottom: 64rpx;
				
				.desc-text {
					display: block;
					font-size: 28rpx;
					color: #9ca3af;
					line-height: 1.6;
					margin-bottom: 8rpx;
				}
			}
			
			/* 装饰元素 */
			.decoration-container {
				position: relative;
				height: 120rpx;
				margin-bottom: 64rpx;
				
				.decoration-item {
					position: absolute;
					animation: float 3s infinite ease-in-out;
					
					.decoration-icon {
						font-size: 48rpx;
						opacity: 0.7;
					}
					
					&:nth-child(1) {
						top: 0;
						left: 20%;
					}
					
					&:nth-child(2) {
						top: 40rpx;
						right: 15%;
					}
					
					&:nth-child(3) {
						bottom: 0;
						left: 15%;
					}
					
					&:nth-child(4) {
						top: 20rpx;
						right: 25%;
					}
				}
			}
		}
	}
}

/* 动画效果 */
@keyframes bounce {
	0%, 20%, 50%, 80%, 100% {
		transform: translateY(0);
	}
	40% {
		transform: translateY(-20rpx);
	}
	60% {
		transform: translateY(-10rpx);
	}
}

@keyframes glow {
	0% {
		opacity: 0.5;
		transform: translate(-50%, -50%) scale(1);
	}
	100% {
		opacity: 0.8;
		transform: translate(-50%, -50%) scale(1.1);
	}
}

@keyframes float {
	0%, 100% {
		transform: translateY(0) rotate(0deg);
	}
	25% {
		transform: translateY(-20rpx) rotate(5deg);
	}
	50% {
		transform: translateY(-10rpx) rotate(-5deg);
	}
	75% {
		transform: translateY(-15rpx) rotate(3deg);
	}
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
	display: none;
}
</style>
